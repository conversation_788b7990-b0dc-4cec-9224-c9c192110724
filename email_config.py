#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
邮件配置管理模块
安全地管理邮箱账户信息和配置
"""

import os
import json
from typing import Dict, Optional
import logging

logger = logging.getLogger(__name__)


class EmailConfig:
    """邮件配置管理类"""
    
    def __init__(self, config_file: str = "email_config.json"):
        """
        初始化配置管理器
        
        Args:
            config_file: 配置文件路径
        """
        self.config_file = config_file
        self.config = self._load_config()
    
    def _load_config(self) -> Dict:
        """加载配置文件"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"加载配置文件失败: {e}")
                return {}
        return {}
    
    def _save_config(self):
        """保存配置文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
            logger.info("配置文件保存成功")
        except Exception as e:
            logger.error(f"保存配置文件失败: {e}")
    
    def add_email_account(self, name: str, email: str, password: str, 
                         smtp_server: str = None, smtp_port: int = None, 
                         use_ssl: bool = True):
        """
        添加邮箱账户配置
        
        Args:
            name: 账户名称（用于标识）
            email: 邮箱地址
            password: 邮箱密码或授权码
            smtp_server: SMTP服务器地址
            smtp_port: SMTP端口
            use_ssl: 是否使用SSL
        """
        if "accounts" not in self.config:
            self.config["accounts"] = {}
        
        # 自动检测SMTP配置
        if not smtp_server or not smtp_port:
            domain = email.split('@')[1].lower()
            smtp_configs = {
                "qq.com": {"server": "smtp.qq.com", "port": 465, "ssl": True},
                "163.com": {"server": "smtp.163.com", "port": 465, "ssl": True},
                "126.com": {"server": "smtp.126.com", "port": 465, "ssl": True},
                "gmail.com": {"server": "smtp.gmail.com", "port": 587, "ssl": False},
                "outlook.com": {"server": "smtp-mail.outlook.com", "port": 587, "ssl": False},
            }
            
            if domain in smtp_configs:
                config = smtp_configs[domain]
                smtp_server = smtp_server or config["server"]
                smtp_port = smtp_port or config["port"]
                use_ssl = config["ssl"]
        
        self.config["accounts"][name] = {
            "email": email,
            "password": password,
            "smtp_server": smtp_server,
            "smtp_port": smtp_port,
            "use_ssl": use_ssl
        }
        
        self._save_config()
        logger.info(f"邮箱账户 {name} 配置已保存")
    
    def get_account(self, name: str) -> Optional[Dict]:
        """
        获取邮箱账户配置
        
        Args:
            name: 账户名称
            
        Returns:
            Dict: 账户配置信息，如果不存在返回None
        """
        return self.config.get("accounts", {}).get(name)
    
    def list_accounts(self) -> Dict:
        """列出所有账户"""
        accounts = self.config.get("accounts", {})
        # 隐藏密码信息
        safe_accounts = {}
        for name, config in accounts.items():
            safe_config = config.copy()
            safe_config["password"] = "***隐藏***"
            safe_accounts[name] = safe_config
        return safe_accounts
    
    def remove_account(self, name: str) -> bool:
        """
        删除邮箱账户配置
        
        Args:
            name: 账户名称
            
        Returns:
            bool: 删除成功返回True
        """
        if "accounts" in self.config and name in self.config["accounts"]:
            del self.config["accounts"][name]
            self._save_config()
            logger.info(f"邮箱账户 {name} 已删除")
            return True
        return False
    
    def update_account_password(self, name: str, new_password: str) -> bool:
        """
        更新账户密码
        
        Args:
            name: 账户名称
            new_password: 新密码
            
        Returns:
            bool: 更新成功返回True
        """
        if "accounts" in self.config and name in self.config["accounts"]:
            self.config["accounts"][name]["password"] = new_password
            self._save_config()
            logger.info(f"邮箱账户 {name} 密码已更新")
            return True
        return False


def setup_qq_account():
    """设置QQ邮箱账户的交互式配置"""
    print("=== QQ邮箱账户配置 ===")
    
    config_manager = EmailConfig()
    
    # 检查是否已有QQ邮箱配置
    existing_qq = config_manager.get_account("qq_main")
    if existing_qq:
        print(f"已存在QQ邮箱配置: {existing_qq['email']}")
        update = input("是否更新配置？(y/n): ").strip().lower()
        if update != 'y':
            return config_manager
    
    # 获取QQ邮箱信息
    qq_email = input("请输入QQ邮箱地址 (默认: <EMAIL>): ").strip()
    if not qq_email:
        qq_email = "<EMAIL>"
    
    print("\n📧 QQ邮箱授权码获取指南：")
    print("1. 登录QQ邮箱 -> 设置 -> 账户")
    print("2. 开启POP3/SMTP服务")
    print("3. 获取16位授权码")
    
    auth_code = input("\n请输入QQ邮箱授权码: ").strip()
    if not auth_code:
        print("❌ 未输入授权码，配置取消")
        return config_manager
    
    # 保存配置
    config_manager.add_email_account(
        name="qq_main",
        email=qq_email,
        password=auth_code,
        smtp_server="smtp.qq.com",
        smtp_port=465,
        use_ssl=True
    )
    
    print("✅ QQ邮箱配置保存成功！")
    return config_manager


def main():
    """主函数 - 配置管理演示"""
    print("📧 邮件配置管理工具")
    print("=" * 30)
    
    config_manager = EmailConfig()
    
    while True:
        print("\n请选择操作:")
        print("1. 添加邮箱账户")
        print("2. 查看所有账户")
        print("3. 设置QQ邮箱")
        print("4. 删除账户")
        print("5. 更新密码")
        print("6. 退出")
        
        choice = input("请输入选择 (1-6): ").strip()
        
        if choice == "1":
            name = input("账户名称: ").strip()
            email = input("邮箱地址: ").strip()
            password = input("密码/授权码: ").strip()
            
            if name and email and password:
                config_manager.add_email_account(name, email, password)
                print("✅ 账户添加成功")
            else:
                print("❌ 信息不完整")
        
        elif choice == "2":
            accounts = config_manager.list_accounts()
            if accounts:
                print("\n📧 已配置的邮箱账户:")
                for name, config in accounts.items():
                    print(f"- {name}: {config['email']} ({config['smtp_server']})")
            else:
                print("暂无配置的邮箱账户")
        
        elif choice == "3":
            setup_qq_account()
        
        elif choice == "4":
            name = input("要删除的账户名称: ").strip()
            if config_manager.remove_account(name):
                print("✅ 账户删除成功")
            else:
                print("❌ 账户不存在")
        
        elif choice == "5":
            name = input("账户名称: ").strip()
            new_password = input("新密码: ").strip()
            if config_manager.update_account_password(name, new_password):
                print("✅ 密码更新成功")
            else:
                print("❌ 账户不存在")
        
        elif choice == "6":
            print("配置管理结束")
            break
        
        else:
            print("无效选择")


if __name__ == "__main__":
    main()
