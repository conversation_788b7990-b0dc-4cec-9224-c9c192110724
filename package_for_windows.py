#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Windows打包准备脚本
创建所有必要的文件和说明，供在Windows环境下打包使用
"""

import os
import shutil


def create_windows_package():
    """创建Windows打包包"""
    package_dir = "windows_package"
    
    # 清理并创建目录
    if os.path.exists(package_dir):
        shutil.rmtree(package_dir)
    os.makedirs(package_dir)
    
    # 复制Python源文件
    source_files = [
        'email_sender.py',
        'email_gui.py',
        'demo.py',
        'test_email.py',
        'README.md'
    ]
    
    for file in source_files:
        if os.path.exists(file):
            shutil.copy2(file, package_dir)
            print(f"✅ 复制文件: {file}")
    
    # 创建requirements.txt for Windows
    requirements_content = """# Windows打包所需依赖
pyinstaller>=5.0
# 其他依赖都是Python标准库，无需额外安装
"""
    
    with open(os.path.join(package_dir, 'requirements_windows.txt'), 'w', encoding='utf-8') as f:
        f.write(requirements_content)
    
    # 创建Windows批处理安装脚本
    install_bat = """@echo off
echo 安装打包依赖...
pip install pyinstaller
echo 依赖安装完成！
pause
"""
    
    with open(os.path.join(package_dir, 'install_dependencies.bat'), 'w', encoding='gbk') as f:
        f.write(install_bat)
    
    # 创建Windows批处理打包脚本
    build_bat = """@echo off
echo 开始打包邮件发送工具...

echo 创建PyInstaller规格文件...
python create_spec.py

echo 开始构建可执行文件...
pyinstaller --clean email_tool.spec

echo 检查构建结果...
if exist "dist\\邮件发送工具.exe" (
    echo 构建成功！
    echo 可执行文件位置: dist\\邮件发送工具.exe
    
    echo 创建便携版...
    if not exist "便携版" mkdir "便携版"
    copy "dist\\邮件发送工具.exe" "便携版\\"
    copy "使用说明.txt" "便携版\\"
    
    echo 打包完成！便携版文件夹已创建。
) else (
    echo 构建失败，请检查错误信息。
)

pause
"""
    
    with open(os.path.join(package_dir, 'build.bat'), 'w', encoding='gbk') as f:
        f.write(build_bat)
    
    # 创建PyInstaller规格文件生成脚本
    create_spec_py = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建PyInstaller规格文件
"""

spec_content = """# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['email_gui.py'],
    pathex=[],
    binaries=[],
    datas=[],
    hiddenimports=[
        'tkinter',
        'tkinter.ttk', 
        'tkinter.messagebox',
        'tkinter.scrolledtext',
        'email.mime.text',
        'email.mime.multipart',
        'email.header',
        'smtplib',
        'ssl',
        'threading',
        'logging'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='邮件发送工具',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
"""

with open('email_tool.spec', 'w', encoding='utf-8') as f:
    f.write(spec_content)

print("✅ PyInstaller规格文件已创建: email_tool.spec")
'''
    
    with open(os.path.join(package_dir, 'create_spec.py'), 'w', encoding='utf-8') as f:
        f.write(create_spec_py)
    
    # 创建使用说明
    usage_txt = """# 邮件发送工具 - Windows打包说明

## 在Windows环境下打包步骤：

### 1. 准备环境
- 确保已安装Python 3.6或更高版本
- 将此文件夹复制到Windows电脑上

### 2. 安装依赖
双击运行: install_dependencies.bat
或者在命令行中运行: pip install pyinstaller

### 3. 开始打包
双击运行: build.bat
或者在命令行中运行以下命令：
```
python create_spec.py
pyinstaller --clean email_tool.spec
```

### 4. 获取结果
打包成功后会生成：
- dist/邮件发送工具.exe (可执行文件)
- 便携版/ (包含exe和说明的文件夹)

## 使用说明

### 邮件发送工具功能：
- 发送方邮箱: <EMAIL> (已预配置)
- 授权码: FPszbLrZiv8Vm7Z3 (已预配置)
- 支持发送到任意目标邮箱
- 图形界面操作简单

### 运行要求：
- Windows 7 或更高版本
- 网络连接
- 无需安装Python环境

## 故障排除

### 如果打包失败：
1. 检查Python版本是否为3.6+
2. 确保PyInstaller安装成功
3. 检查所有源文件是否完整
4. 查看错误信息并根据提示解决

### 如果运行失败：
1. 检查网络连接
2. 确认目标邮箱地址格式正确
3. 检查防火墙设置

## 文件说明

- email_gui.py: 图形界面主程序
- email_sender.py: 邮件发送核心模块
- demo.py: 命令行演示程序
- test_email.py: 测试程序
- create_spec.py: PyInstaller配置生成器
- install_dependencies.bat: 依赖安装脚本
- build.bat: 一键打包脚本

## 技术支持

如有问题，请检查：
1. Python环境是否正确安装
2. 网络连接是否正常
3. 所有文件是否完整
4. PyInstaller是否正确安装
"""
    
    with open(os.path.join(package_dir, '使用说明.txt'), 'w', encoding='utf-8') as f:
        f.write(usage_txt)
    
    # 创建最终用户使用说明
    user_guide = """# 邮件发送工具使用指南

## 快速开始

1. 双击"邮件发送工具.exe"启动程序
2. 程序界面包含以下字段：
   - 发送方邮箱: <EMAIL> (已预设)
   - 邮箱授权码: 已预设，无需修改
   - 目标邮箱: 请输入收件人邮箱地址
   - 邮件标题: 请输入邮件主题
   - 邮件正文: 请输入邮件内容

3. 填写完成后点击"发送邮件"按钮

## 注意事项

- 确保电脑已连接互联网
- 目标邮箱地址必须格式正确
- 邮件标题和内容不能为空
- 发送过程中请耐心等待

## 常见问题

Q: 提示发送失败怎么办？
A: 请检查网络连接和目标邮箱地址是否正确

Q: 程序无法启动？
A: 请确认Windows版本为7或更高，并检查防病毒软件是否误报

Q: 可以发送给多个人吗？
A: 当前版本一次只能发送给一个收件人，如需群发请多次发送

## 系统要求

- Windows 7/8/10/11
- 网络连接
- 无需安装额外软件
"""
    
    with open(os.path.join(package_dir, '最终用户使用指南.txt'), 'w', encoding='utf-8') as f:
        f.write(user_guide)
    
    print(f"✅ Windows打包包已创建: {package_dir}")
    print("\n📦 包含文件:")
    for root, dirs, files in os.walk(package_dir):
        for file in files:
            print(f"   - {file}")
    
    print(f"\n🚀 使用方法:")
    print(f"1. 将 '{package_dir}' 文件夹复制到Windows电脑")
    print(f"2. 在Windows上双击运行 'install_dependencies.bat'")
    print(f"3. 然后双击运行 'build.bat' 开始打包")
    print(f"4. 打包完成后在 'dist' 目录找到可执行文件")


if __name__ == "__main__":
    create_windows_package()
