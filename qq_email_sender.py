#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
QQ邮箱发送器 - 合法使用QQ邮箱SMTP服务发送邮件
支持使用您自己的QQ邮箱****************发送邮件
"""

from email_sender import EmailSender
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class QQEmailSender(EmailSender):
    """QQ邮箱专用发送器"""
    
    def __init__(self, qq_email: str = "<EMAIL>", qq_auth_code: str = None):
        """
        初始化QQ邮箱发送器
        
        Args:
            qq_email: QQ邮箱地址
            qq_auth_code: QQ邮箱授权码（需要在QQ邮箱设置中获取）
        """
        if not qq_auth_code:
            raise ValueError("必须提供QQ邮箱授权码！请在QQ邮箱设置中开启SMTP服务并获取授权码。")
        
        # 调用父类初始化
        super().__init__(sender_email=qq_email, sender_password=qq_auth_code)
        
        logger.info(f"QQ邮箱发送器初始化完成: {qq_email}")
    
    def send_as_qq(self, to_email: str, subject: str, content: str, 
                   sender_name: str = "QQ用户", content_type: str = "plain") -> bool:
        """
        使用QQ邮箱发送邮件，带有自定义发件人名称
        
        Args:
            to_email: 收件人邮箱
            subject: 邮件主题
            content: 邮件内容
            sender_name: 发件人显示名称
            content_type: 内容类型 ("plain" 或 "html")
            
        Returns:
            bool: 发送成功返回True，失败返回False
        """
        return self.send_email(
            to_email=to_email,
            subject=subject,
            content=content,
            content_type=content_type,
            sender_name=sender_name
        )
    
    def send_notification_email(self, to_email: str, title: str, message: str) -> bool:
        """
        发送通知邮件
        
        Args:
            to_email: 收件人邮箱
            title: 通知标题
            message: 通知内容
            
        Returns:
            bool: 发送成功返回True，失败返回False
        """
        subject = f"📧 通知: {title}"
        
        content = f"""
您好！

您收到一条新的通知：

标题：{title}
内容：{message}

发送时间：{__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

此邮件由QQ邮箱自动发送，请勿回复。
        """.strip()
        
        return self.send_as_qq(
            to_email=to_email,
            subject=subject,
            content=content,
            sender_name="系统通知"
        )
    
    def send_html_notification(self, to_email: str, title: str, message: str) -> bool:
        """
        发送HTML格式的通知邮件
        
        Args:
            to_email: 收件人邮箱
            title: 通知标题
            message: 通知内容
            
        Returns:
            bool: 发送成功返回True，失败返回False
        """
        subject = f"📧 通知: {title}"
        
        html_content = f"""
        <html>
        <head>
            <meta charset="utf-8">
            <style>
                body {{ font-family: Arial, sans-serif; line-height: 1.6; margin: 0; padding: 20px; }}
                .container {{ max-width: 600px; margin: 0 auto; background: #f9f9f9; padding: 20px; border-radius: 10px; }}
                .header {{ background: #1aad19; color: white; padding: 15px; text-align: center; border-radius: 5px; margin-bottom: 20px; }}
                .content {{ background: white; padding: 20px; border-radius: 5px; margin-bottom: 20px; }}
                .footer {{ text-align: center; color: #666; font-size: 12px; }}
                .highlight {{ background: #e7f3ff; padding: 10px; border-left: 4px solid #1aad19; margin: 10px 0; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>📧 系统通知</h1>
                </div>
                <div class="content">
                    <h2>{title}</h2>
                    <div class="highlight">
                        <p>{message}</p>
                    </div>
                    <p><strong>发送时间：</strong>{__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
                </div>
                <div class="footer">
                    <p>此邮件由QQ邮箱自动发送，请勿回复。</p>
                </div>
            </div>
        </body>
        </html>
        """
        
        return self.send_as_qq(
            to_email=to_email,
            subject=subject,
            content=html_content,
            content_type="html",
            sender_name="系统通知"
        )


def get_qq_auth_code_guide():
    """获取QQ邮箱授权码的指南"""
    guide = """
    📧 QQ邮箱授权码获取指南：
    
    1. 登录QQ邮箱 (https://mail.qq.com)
    2. 点击右上角"设置" -> "账户"
    3. 找到"POP3/IMAP/SMTP/Exchange/CardDAV/CalDAV服务"
    4. 开启"POP3/SMTP服务"或"IMAP/SMTP服务"
    5. 按照提示发送短信验证
    6. 获得16位授权码（类似：abcdefghijklmnop）
    7. 使用此授权码作为邮箱密码
    
    ⚠️ 注意：
    - 授权码不是QQ密码
    - 请妥善保管授权码
    - 每次重新生成授权码，旧的会失效
    """
    return guide


def main():
    """主函数 - 演示QQ邮箱发送功能"""
    print("=== QQ邮箱发送器演示 ===")
    print(get_qq_auth_code_guide())
    
    # 获取QQ邮箱授权码
    qq_auth_code = input("\n请输入您的QQ邮箱授权码: ").strip()
    if not qq_auth_code:
        print("❌ 未输入授权码，程序退出")
        return
    
    try:
        # 创建QQ邮箱发送器
        qq_sender = QQEmailSender(qq_auth_code=qq_auth_code)
        
        # 获取目标邮箱
        target_email = input("请输入目标邮箱: ").strip()
        if not target_email:
            print("❌ 未输入目标邮箱，程序退出")
            return
        
        # 选择发送类型
        print("\n请选择发送类型:")
        print("1. 普通文本邮件")
        print("2. HTML格式邮件")
        print("3. 系统通知邮件")
        print("4. HTML通知邮件")
        
        choice = input("请输入选择 (1-4): ").strip()
        
        if choice == "1":
            subject = input("请输入邮件标题: ").strip()
            content = input("请输入邮件内容: ").strip()
            sender_name = input("请输入发件人显示名称 (默认: QQ用户): ").strip() or "QQ用户"
            
            success = qq_sender.send_as_qq(target_email, subject, content, sender_name)
            
        elif choice == "2":
            subject = input("请输入邮件标题: ").strip()
            content = input("请输入HTML内容: ").strip()
            sender_name = input("请输入发件人显示名称 (默认: QQ用户): ").strip() or "QQ用户"
            
            success = qq_sender.send_as_qq(target_email, subject, content, sender_name, "html")
            
        elif choice == "3":
            title = input("请输入通知标题: ").strip()
            message = input("请输入通知内容: ").strip()
            
            success = qq_sender.send_notification_email(target_email, title, message)
            
        elif choice == "4":
            title = input("请输入通知标题: ").strip()
            message = input("请输入通知内容: ").strip()
            
            success = qq_sender.send_html_notification(target_email, title, message)
            
        else:
            print("❌ 无效选择")
            return
        
        if success:
            print("✅ 邮件发送成功！")
        else:
            print("❌ 邮件发送失败！")
            
    except Exception as e:
        print(f"❌ 程序运行出错: {e}")


if __name__ == "__main__":
    main()
