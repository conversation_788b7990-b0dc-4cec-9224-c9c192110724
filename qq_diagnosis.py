#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
QQ邮箱问题深度诊断
"""

import smtplib
import ssl
import socket
import logging

# 设置详细日志
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_network_connectivity():
    """测试网络连接"""
    print("=== 网络连接测试 ===")
    
    hosts = [
        ("smtp.qq.com", 465),
        ("smtp.qq.com", 587),
        ("smtp.qq.com", 25),
    ]
    
    for host, port in hosts:
        try:
            print(f"🔍 测试连接到 {host}:{port}")
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(10)
            result = sock.connect_ex((host, port))
            sock.close()
            
            if result == 0:
                print(f"  ✅ {host}:{port} 连接成功")
            else:
                print(f"  ❌ {host}:{port} 连接失败 (错误码: {result})")
        except Exception as e:
            print(f"  ❌ {host}:{port} 连接异常: {e}")

def test_smtp_handshake():
    """测试SMTP握手"""
    print("\n=== SMTP握手测试 ===")
    
    configs = [
        {"host": "smtp.qq.com", "port": 465, "ssl": True},
        {"host": "smtp.qq.com", "port": 587, "ssl": False},
    ]
    
    for config in configs:
        try:
            print(f"🔍 测试 {config['host']}:{config['port']} ({'SSL' if config['ssl'] else 'TLS'})")
            
            if config['ssl']:
                # SSL连接
                context = ssl.create_default_context()
                context.check_hostname = False
                context.verify_mode = ssl.CERT_NONE
                
                server = smtplib.SMTP_SSL(config['host'], config['port'], context=context)
                print(f"  ✅ SSL连接建立成功")
            else:
                # TLS连接
                server = smtplib.SMTP(config['host'], config['port'])
                print(f"  📡 SMTP连接建立")
                
                # 获取服务器响应
                response = server.ehlo()
                print(f"  📋 EHLO响应: {response}")
                
                # 启动TLS
                context = ssl.create_default_context()
                context.check_hostname = False
                context.verify_mode = ssl.CERT_NONE
                
                tls_response = server.starttls(context=context)
                print(f"  🔐 TLS响应: {tls_response}")
                
                # 再次EHLO
                response2 = server.ehlo()
                print(f"  📋 TLS后EHLO响应: {response2}")
            
            # 获取服务器能力
            print(f"  🔧 服务器能力: {server.esmtp_features}")
            
            server.quit()
            print(f"  ✅ {config['host']}:{config['port']} 握手成功")
            
        except Exception as e:
            print(f"  ❌ {config['host']}:{config['port']} 握手失败: {e}")
            logger.error(f"SMTP握手失败: {e}")

def test_qq_login():
    """测试QQ邮箱登录"""
    print("\n=== QQ邮箱登录测试 ===")
    
    qq_email = "<EMAIL>"
    qq_auth_code = "frkpmkdrehsxbdac"
    
    configs = [
        {"host": "smtp.qq.com", "port": 465, "ssl": True, "name": "SSL"},
        {"host": "smtp.qq.com", "port": 587, "ssl": False, "name": "TLS"},
    ]
    
    for config in configs:
        try:
            print(f"🔍 测试 {config['name']} 登录")
            
            context = ssl.create_default_context()
            context.check_hostname = False
            context.verify_mode = ssl.CERT_NONE
            
            if config['ssl']:
                server = smtplib.SMTP_SSL(config['host'], config['port'], context=context)
            else:
                server = smtplib.SMTP(config['host'], config['port'])
                server.ehlo()
                server.starttls(context=context)
                server.ehlo()
            
            print(f"  🔑 尝试登录 {qq_email}")
            login_response = server.login(qq_email, qq_auth_code)
            print(f"  ✅ {config['name']} 登录成功: {login_response}")
            
            server.quit()
            return config
            
        except Exception as e:
            print(f"  ❌ {config['name']} 登录失败: {e}")
            logger.error(f"登录失败: {e}")
    
    return None

def test_simple_send():
    """测试简单邮件发送"""
    print("\n=== 简单邮件发送测试 ===")
    
    qq_email = "<EMAIL>"
    qq_auth_code = "frkpmkdrehsxbdac"
    target_email = "<EMAIL>"  # 改为163邮箱测试
    
    try:
        # 使用最简单的方式
        context = ssl.create_default_context()
        context.check_hostname = False
        context.verify_mode = ssl.CERT_NONE
        
        print("📧 使用SSL连接发送简单邮件")
        with smtplib.SMTP_SSL("smtp.qq.com", 465, context=context) as server:
            server.login(qq_email, qq_auth_code)
            
            # 最简单的邮件 - 使用英文避免编码问题
            subject = "QQ Email Test"
            body = "This is a test email from QQ mailbox."
            message = f"Subject: {subject}\n\n{body}"
            
            print(f"📤 发送到 {target_email}")
            result = server.sendmail(qq_email, [target_email], message)
            print(f"✅ 发送结果: {result}")
            
        return True
        
    except Exception as e:
        print(f"❌ 简单发送失败: {e}")
        logger.error(f"简单发送失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 QQ邮箱深度诊断工具")
    print("=" * 40)
    
    # 1. 测试网络连接
    test_network_connectivity()
    
    # 2. 测试SMTP握手
    test_smtp_handshake()
    
    # 3. 测试登录
    working_config = test_qq_login()
    
    # 4. 如果登录成功，测试发送
    if working_config:
        print(f"\n🎉 找到可用配置: {working_config['name']}")
        test_simple_send()
    else:
        print("\n❌ 所有登录尝试都失败了")
        
        print("\n🔧 可能的问题:")
        print("1. 授权码错误或已过期")
        print("2. QQ邮箱SMTP服务未开启")
        print("3. 网络防火墙阻止连接")
        print("4. QQ邮箱安全策略限制")

if __name__ == "__main__":
    main()
