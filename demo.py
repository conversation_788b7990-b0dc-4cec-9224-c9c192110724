#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
邮件发送功能演示脚本
"""

from email_sender import EmailSender


def demo_send_email():
    """演示发送邮件功能"""
    print("=== 邮件发送功能演示 ===")
    
    # 创建邮件发送器（已预配置授权码）
    sender = EmailSender()
    
    print(f"发送方邮箱: {sender.sender_email}")
    print("授权码已预配置")
    
    # 获取目标邮箱
    to_email = input("\n请输入目标邮箱地址: ").strip()
    if not to_email:
        print("未输入目标邮箱，使用默认邮箱进行演示")
        to_email = "<EMAIL>"
    
    # 获取邮件标题
    subject = input("请输入邮件标题 (默认: 测试邮件): ").strip()
    if not subject:
        subject = "测试邮件"
    
    # 获取邮件内容
    print("请输入邮件内容 (输入空行结束):")
    content_lines = []
    while True:
        line = input()
        if line == "":
            break
        content_lines.append(line)
    
    content = "\n".join(content_lines) if content_lines else "这是一封测试邮件，发送时间: " + str(__import__('datetime').datetime.now())
    
    # 发送邮件
    print(f"\n正在发送邮件到: {to_email}")
    print(f"邮件标题: {subject}")
    print(f"邮件内容: {content[:50]}{'...' if len(content) > 50 else ''}")
    
    success = sender.send_email(
        to_email=to_email,
        subject=subject,
        content=content
    )
    
    if success:
        print("\n✅ 邮件发送成功！")
        print("请检查目标邮箱是否收到邮件")
    else:
        print("\n❌ 邮件发送失败！")
        print("请检查网络连接和邮箱配置")


def demo_send_html_email():
    """演示发送HTML邮件"""
    print("\n=== HTML邮件发送演示 ===")
    
    sender = EmailSender()
    
    to_email = input("请输入目标邮箱地址: ").strip()
    if not to_email:
        to_email = "<EMAIL>"
    
    # HTML邮件内容
    html_content = """
    <html>
    <head>
        <style>
            body { font-family: Arial, sans-serif; }
            .header { background-color: #4CAF50; color: white; padding: 10px; text-align: center; }
            .content { padding: 20px; }
            .footer { background-color: #f1f1f1; padding: 10px; text-align: center; font-size: 12px; }
        </style>
    </head>
    <body>
        <div class="header">
            <h1>HTML邮件测试</h1>
        </div>
        <div class="content">
            <h2>欢迎使用邮件发送功能！</h2>
            <p>这是一封<strong>HTML格式</strong>的测试邮件。</p>
            <ul>
                <li>支持HTML格式</li>
                <li>支持CSS样式</li>
                <li>支持图片和链接</li>
            </ul>
            <p>发送时间: """ + str(__import__('datetime').datetime.now()) + """</p>
        </div>
        <div class="footer">
            <p>此邮件由Python邮件发送工具生成</p>
        </div>
    </body>
    </html>
    """
    
    print(f"正在发送HTML邮件到: {to_email}")
    
    success = sender.send_html_email(
        to_email=to_email,
        subject="HTML邮件测试",
        html_content=html_content
    )
    
    if success:
        print("✅ HTML邮件发送成功！")
    else:
        print("❌ HTML邮件发送失败！")


def main():
    """主函数"""
    print("邮件发送功能演示程序")
    print("发送方邮箱: <EMAIL>")
    print("授权码: FPszbLrZiv8Vm7Z3 (已预配置)")
    
    while True:
        print("\n请选择演示功能:")
        print("1. 发送普通文本邮件")
        print("2. 发送HTML格式邮件")
        print("3. 启动图形界面")
        print("4. 退出")
        
        choice = input("请输入选择 (1-4): ").strip()
        
        if choice == "1":
            demo_send_email()
        elif choice == "2":
            demo_send_html_email()
        elif choice == "3":
            print("启动图形界面...")
            import subprocess
            subprocess.Popen(["python3", "email_gui.py"])
            print("图形界面已启动")
        elif choice == "4":
            print("再见！")
            break
        else:
            print("无效选择，请重新输入")


if __name__ == "__main__":
    main()
