#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接QQ邮箱发送测试 - 使用修复的配置
"""

import smtplib
import ssl
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.header import Header
from email.utils import formatdate
import uuid
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def send_qq_email_direct():
    """直接发送QQ邮箱测试"""
    print("=== 直接QQ邮箱发送测试 ===")
    
    # QQ邮箱配置
    qq_email = "<EMAIL>"
    qq_auth_code = "frkpmkdrehsxbdac"
    target_email = "<EMAIL>"
    
    # SMTP配置 - 使用SSL连接
    smtp_server = "smtp.qq.com"
    smtp_port = 465
    
    try:
        # 创建邮件
        message = MIMEMultipart()
        message["From"] = f"QQ邮箱测试 <{qq_email}>"
        message["To"] = target_email
        message["Subject"] = Header("直接QQ邮箱发送测试", 'utf-8')
        message["Message-ID"] = f"<{uuid.uuid4()}@qq.com>"
        message["Date"] = formatdate(localtime=True)
        message["Sender"] = qq_email
        
        # 邮件内容
        content = """
QQ邮箱直接发送测试成功！

测试信息：
- 发送方：<EMAIL>
- 接收方：<EMAIL>
- 连接方式：SSL (465端口)
- 发送时间：2025-07-24

这封邮件证明QQ邮箱发送功能已修复。
        """.strip()
        
        message.attach(MIMEText(content, 'plain', 'utf-8'))
        
        # 创建SSL上下文
        context = ssl.create_default_context()
        context.check_hostname = False
        context.verify_mode = ssl.CERT_NONE
        
        print(f"📧 连接到 {smtp_server}:{smtp_port}")

        # 使用SSL连接发送邮件
        with smtplib.SMTP_SSL(smtp_server, smtp_port, context=context) as server:
            print("🔑 登录QQ邮箱...")
            server.login(qq_email, qq_auth_code)

            print("📤 发送邮件...")
            text = message.as_string()
            server.sendmail(qq_email, [target_email], text)
        
        print("✅ 邮件发送成功！")
        logger.info(f"邮件发送成功: {target_email}")
        return True
        
    except Exception as e:
        print(f"❌ 邮件发送失败: {e}")
        logger.error(f"发送失败详情: {e}")
        return False

def main():
    """主函数"""
    print("🚀 QQ邮箱直接发送测试")
    print("=" * 30)
    
    success = send_qq_email_direct()
    
    if success:
        print("\n🎉 测试成功！QQ邮箱发送功能正常")
    else:
        print("\n❌ 测试失败！需要进一步排查问题")

if __name__ == "__main__":
    main()
