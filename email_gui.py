#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
邮件发送GUI界面
基于tkinter创建图形用户界面
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
from email_sender import EmailSender


class EmailGUI:
    """邮件发送GUI类"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("邮件发送工具")
        self.root.geometry("600x500")
        self.root.resizable(True, True)
        
        # 创建邮件发送器实例
        self.email_sender = None
        
        self.create_widgets()
        
    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # 发送方邮箱配置
        ttk.Label(main_frame, text="发送方邮箱:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.sender_email_var = tk.StringVar(value="<EMAIL>")
        ttk.Entry(main_frame, textvariable=self.sender_email_var, width=40).grid(row=0, column=1, sticky=(tk.W, tk.E), pady=5)
        
        # 邮箱授权码
        ttk.Label(main_frame, text="邮箱授权码:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.password_var = tk.StringVar(value="FPszbLrZiv8Vm7Z3")
        password_entry = ttk.Entry(main_frame, textvariable=self.password_var, show="*", width=40)
        password_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=5)
        
        # 分隔线
        ttk.Separator(main_frame, orient='horizontal').grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=10)
        
        # 目标邮箱
        ttk.Label(main_frame, text="目标邮箱:").grid(row=3, column=0, sticky=tk.W, pady=5)
        self.to_email_var = tk.StringVar()
        ttk.Entry(main_frame, textvariable=self.to_email_var, width=40).grid(row=3, column=1, sticky=(tk.W, tk.E), pady=5)
        
        # 邮件标题
        ttk.Label(main_frame, text="邮件标题:").grid(row=4, column=0, sticky=tk.W, pady=5)
        self.subject_var = tk.StringVar()
        ttk.Entry(main_frame, textvariable=self.subject_var, width=40).grid(row=4, column=1, sticky=(tk.W, tk.E), pady=5)
        
        # 邮件正文
        ttk.Label(main_frame, text="邮件正文:").grid(row=5, column=0, sticky=(tk.W, tk.N), pady=5)
        
        # 创建文本框框架
        text_frame = ttk.Frame(main_frame)
        text_frame.grid(row=5, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), pady=5)
        text_frame.columnconfigure(0, weight=1)
        text_frame.rowconfigure(0, weight=1)
        
        # 邮件内容文本框
        self.content_text = scrolledtext.ScrolledText(text_frame, height=10, width=50, wrap=tk.WORD)
        self.content_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置主框架的行权重
        main_frame.rowconfigure(5, weight=1)
        
        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=6, column=0, columnspan=2, pady=20)
        
        # 发送按钮
        self.send_button = ttk.Button(button_frame, text="发送邮件", command=self.send_email)
        self.send_button.pack(side=tk.LEFT, padx=5)
        
        # 清空按钮
        ttk.Button(button_frame, text="清空内容", command=self.clear_content).pack(side=tk.LEFT, padx=5)
        
        # 状态标签
        self.status_label = ttk.Label(main_frame, text="就绪", foreground="blue")
        self.status_label.grid(row=7, column=0, columnspan=2, pady=5)
        
    def validate_inputs(self):
        """验证输入"""
        if not self.sender_email_var.get().strip():
            messagebox.showerror("错误", "请输入发送方邮箱")
            return False
            
        if not self.password_var.get().strip():
            messagebox.showerror("错误", "请输入邮箱授权码")
            return False
            
        if not self.to_email_var.get().strip():
            messagebox.showerror("错误", "请输入目标邮箱")
            return False
            
        if not self.subject_var.get().strip():
            messagebox.showerror("错误", "请输入邮件标题")
            return False
            
        if not self.content_text.get("1.0", tk.END).strip():
            messagebox.showerror("错误", "请输入邮件内容")
            return False
            
        return True
    
    def send_email(self):
        """发送邮件"""
        if not self.validate_inputs():
            return
            
        # 禁用发送按钮
        self.send_button.config(state="disabled")
        self.status_label.config(text="正在发送邮件...", foreground="orange")
        
        # 在新线程中发送邮件，避免界面卡顿
        thread = threading.Thread(target=self._send_email_thread)
        thread.daemon = True
        thread.start()
    
    def _send_email_thread(self):
        """在线程中发送邮件"""
        try:
            # 创建邮件发送器
            self.email_sender = EmailSender(
                sender_email=self.sender_email_var.get().strip(),
                sender_password=self.password_var.get().strip()
            )
            
            # 发送邮件
            success = self.email_sender.send_email(
                to_email=self.to_email_var.get().strip(),
                subject=self.subject_var.get().strip(),
                content=self.content_text.get("1.0", tk.END).strip()
            )
            
            # 更新UI（需要在主线程中执行）
            self.root.after(0, self._update_send_result, success)
            
        except Exception as e:
            self.root.after(0, self._update_send_result, False, str(e))
    
    def _update_send_result(self, success, error_msg=None):
        """更新发送结果"""
        self.send_button.config(state="normal")
        
        if success:
            self.status_label.config(text="✅ 邮件发送成功！", foreground="green")
            messagebox.showinfo("成功", "邮件发送成功！")
        else:
            error_text = f"❌ 邮件发送失败"
            if error_msg:
                error_text += f": {error_msg}"
            self.status_label.config(text=error_text, foreground="red")
            messagebox.showerror("失败", f"邮件发送失败\n{error_msg if error_msg else ''}")
    
    def clear_content(self):
        """清空内容"""
        self.to_email_var.set("")
        self.subject_var.set("")
        self.content_text.delete("1.0", tk.END)
        self.status_label.config(text="内容已清空", foreground="blue")


def main():
    """主函数"""
    root = tk.Tk()
    app = EmailGUI(root)
    root.mainloop()


if __name__ == "__main__":
    main()
