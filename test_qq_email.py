#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试发送到QQ邮箱的脚本
解决邮件退回问题
"""

from email_sender import EmailSender
import logging

# 设置详细日志
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def test_qq_email():
    """测试发送到QQ邮箱"""
    print("=== 测试发送到QQ邮箱 ===")
    
    # 创建邮件发送器
    sender = EmailSender()
    
    # 目标QQ邮箱
    qq_email = "<EMAIL>"
    
    # 邮件内容
    subject = "测试邮件 - 解决退回问题"
    content = """您好！

这是一封测试邮件，用于验证邮件发送功能是否正常。

邮件内容：
- 发送方：<EMAIL>
- 接收方：<EMAIL>
- 发送时间：""" + str(__import__('datetime').datetime.now()) + """

如果您收到这封邮件，说明邮件发送功能正常工作。

谢谢！
"""
    
    print(f"发送方邮箱: {sender.sender_email}")
    print(f"目标邮箱: {qq_email}")
    print(f"邮件标题: {subject}")
    print("正在发送邮件...")
    
    try:
        # 发送邮件
        success = sender.send_email(
            to_email=qq_email,
            subject=subject,
            content=content
        )
        
        if success:
            print("✅ 邮件发送成功！")
            print("请检查QQ邮箱是否收到邮件")
        else:
            print("❌ 邮件发送失败！")
            
    except Exception as e:
        print(f"❌ 发送过程中出现异常: {e}")
        logger.error(f"邮件发送异常: {e}")


def test_multiple_qq_emails():
    """测试发送到多个QQ邮箱"""
    print("\n=== 测试发送到多个QQ邮箱 ===")
    
    sender = EmailSender()
    
    # 测试邮箱列表
    test_emails = [
        "<EMAIL>",
        # 可以添加更多测试邮箱
    ]
    
    for email in test_emails:
        print(f"\n正在发送到: {email}")
        
        success = sender.send_email(
            to_email=email,
            subject="批量测试邮件",
            content=f"这是发送到 {email} 的测试邮件。\n发送时间: {__import__('datetime').datetime.now()}"
        )
        
        if success:
            print(f"✅ 发送到 {email} 成功")
        else:
            print(f"❌ 发送到 {email} 失败")


def test_html_email_to_qq():
    """测试发送HTML邮件到QQ邮箱"""
    print("\n=== 测试发送HTML邮件到QQ邮箱 ===")
    
    sender = EmailSender()
    
    html_content = """
    <html>
    <head>
        <meta charset="utf-8">
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; }
            .header { background-color: #4CAF50; color: white; padding: 15px; text-align: center; }
            .content { padding: 20px; background-color: #f9f9f9; }
            .footer { background-color: #333; color: white; padding: 10px; text-align: center; }
            .highlight { background-color: #ffeb3b; padding: 5px; }
        </style>
    </head>
    <body>
        <div class="header">
            <h1>🎉 HTML邮件测试</h1>
        </div>
        <div class="content">
            <h2>邮件发送测试</h2>
            <p>这是一封<strong>HTML格式</strong>的测试邮件。</p>
            
            <div class="highlight">
                <p><strong>重要信息：</strong>此邮件用于测试163邮箱到QQ邮箱的发送功能。</p>
            </div>
            
            <h3>测试信息：</h3>
            <ul>
                <li>发送方：<EMAIL></li>
                <li>接收方：<EMAIL></li>
                <li>邮件格式：HTML</li>
                <li>发送时间：""" + str(__import__('datetime').datetime.now()) + """</li>
            </ul>
            
            <p>如果您能正常查看此邮件的格式和样式，说明HTML邮件发送功能正常。</p>
        </div>
        <div class="footer">
            <p>此邮件由Python邮件发送工具自动生成</p>
        </div>
    </body>
    </html>
    """
    
    success = sender.send_html_email(
        to_email="<EMAIL>",
        subject="HTML格式测试邮件 - 解决退回问题",
        html_content=html_content
    )
    
    if success:
        print("✅ HTML邮件发送成功！")
    else:
        print("❌ HTML邮件发送失败！")


def main():
    """主函数"""
    print("QQ邮箱发送测试程序")
    print("目标：解决邮件退回问题")
    print("=" * 50)
    
    while True:
        print("\n请选择测试选项:")
        print("1. 发送普通文本邮件到QQ邮箱")
        print("2. 发送HTML邮件到QQ邮箱")
        print("3. 批量测试发送")
        print("4. 退出")
        
        choice = input("请输入选择 (1-4): ").strip()
        
        if choice == "1":
            test_qq_email()
        elif choice == "2":
            test_html_email_to_qq()
        elif choice == "3":
            test_multiple_qq_emails()
        elif choice == "4":
            print("测试结束，再见！")
            break
        else:
            print("无效选择，请重新输入")


if __name__ == "__main__":
    main()
