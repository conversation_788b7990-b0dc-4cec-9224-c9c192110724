#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版邮件发送器 - 实现自定义发件人显示功能
使用163邮箱发送，但可以自定义发件人显示名称
"""

from email_sender import EmailSender
import logging
from typing import Optional

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class EnhancedEmailSender(EmailSender):
    """增强版邮件发送器 - 支持自定义发件人显示"""
    
    def __init__(self, sender_email: str = "<EMAIL>", sender_password: str = "FPszbLrZiv8Vm7Z3"):
        """
        初始化增强版邮件发送器
        
        Args:
            sender_email: 实际发送邮箱（163邮箱）
            sender_password: 邮箱授权码
        """
        super().__init__(sender_email, sender_password)
        logger.info(f"增强版邮件发送器初始化完成: {sender_email}")
    
    def send_as_custom_sender(self, 
                             to_email: str, 
                             subject: str, 
                             content: str,
                             display_name: str = "QQ用户",
                             display_email: str = "<EMAIL>",
                             content_type: str = "plain") -> bool:
        """
        以自定义发件人身份发送邮件
        
        Args:
            to_email: 收件人邮箱
            subject: 邮件主题
            content: 邮件内容
            display_name: 显示的发件人名称
            display_email: 显示的发件人邮箱
            content_type: 内容类型 ("plain" 或 "html")
            
        Returns:
            bool: 发送成功返回True，失败返回False
        """
        # 在邮件内容中添加说明
        enhanced_content = self._enhance_content(content, display_name, display_email)
        
        return self.send_email(
            to_email=to_email,
            subject=subject,
            content=enhanced_content,
            content_type=content_type,
            sender_name=f"{display_name} (via {display_email})"
        )
    
    def _enhance_content(self, content: str, display_name: str, display_email: str) -> str:
        """增强邮件内容，添加发件人信息"""
        header = f"发件人: {display_name} <{display_email}>\n"
        header += f"通过: {self.sender_email}\n"
        header += "-" * 50 + "\n\n"
        
        return header + content
    
    def send_qq_style_email(self, 
                           to_email: str, 
                           subject: str, 
                           content: str,
                           qq_number: str = "937359046") -> bool:
        """
        发送QQ风格的邮件
        
        Args:
            to_email: 收件人邮箱
            subject: 邮件主题
            content: 邮件内容
            qq_number: QQ号码
            
        Returns:
            bool: 发送成功返回True，失败返回False
        """
        display_name = f"QQ用户{qq_number}"
        display_email = f"{qq_number}@qq.com"
        
        return self.send_as_custom_sender(
            to_email=to_email,
            subject=subject,
            content=content,
            display_name=display_name,
            display_email=display_email
        )
    
    def send_business_email(self, 
                           to_email: str, 
                           subject: str, 
                           content: str,
                           sender_role: str = "客服") -> bool:
        """
        发送商务邮件
        
        Args:
            to_email: 收件人邮箱
            subject: 邮件主题
            content: 邮件内容
            sender_role: 发件人角色
            
        Returns:
            bool: 发送成功返回True，失败返回False
        """
        return self.send_as_custom_sender(
            to_email=to_email,
            subject=subject,
            content=content,
            display_name=sender_role,
            display_email="<EMAIL>"
        )
    
    def send_notification_email(self, 
                               to_email: str, 
                               title: str, 
                               message: str,
                               sender_name: str = "系统通知") -> bool:
        """
        发送通知邮件
        
        Args:
            to_email: 收件人邮箱
            title: 通知标题
            message: 通知内容
            sender_name: 发件人名称
            
        Returns:
            bool: 发送成功返回True，失败返回False
        """
        subject = f"📧 {title}"
        
        content = f"""
{title}

{message}

发送时间：{__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

此邮件由系统自动发送。
        """.strip()
        
        return self.send_as_custom_sender(
            to_email=to_email,
            subject=subject,
            content=content,
            display_name=sender_name,
            display_email="<EMAIL>"
        )
    
    def send_html_email_with_custom_sender(self, 
                                          to_email: str, 
                                          subject: str, 
                                          html_content: str,
                                          display_name: str = "QQ用户",
                                          display_email: str = "<EMAIL>") -> bool:
        """
        发送HTML格式邮件，带自定义发件人
        
        Args:
            to_email: 收件人邮箱
            subject: 邮件主题
            html_content: HTML内容
            display_name: 显示的发件人名称
            display_email: 显示的发件人邮箱
            
        Returns:
            bool: 发送成功返回True，失败返回False
        """
        # 在HTML内容中添加发件人信息
        enhanced_html = self._enhance_html_content(html_content, display_name, display_email)
        
        return self.send_email(
            to_email=to_email,
            subject=subject,
            content=enhanced_html,
            content_type="html",
            sender_name=f"{display_name} (via {display_email})"
        )
    
    def _enhance_html_content(self, html_content: str, display_name: str, display_email: str) -> str:
        """增强HTML邮件内容"""
        header_html = f"""
        <div style="background: #f0f8ff; padding: 10px; border-left: 4px solid #1aad19; margin-bottom: 20px;">
            <p><strong>发件人:</strong> {display_name} &lt;{display_email}&gt;</p>
            <p><strong>通过:</strong> {self.sender_email}</p>
        </div>
        """
        
        # 如果HTML内容包含body标签，插入到body开始后
        if "<body>" in html_content.lower():
            return html_content.replace("<body>", f"<body>{header_html}", 1)
        else:
            return header_html + html_content


def main():
    """主函数 - 演示增强版邮件发送功能"""
    print("=== 增强版邮件发送器演示 ===")
    
    # 创建增强版发送器
    sender = EnhancedEmailSender()
    
    while True:
        print("\n请选择发送类型:")
        print("1. QQ风格邮件")
        print("2. 自定义发件人邮件")
        print("3. 商务邮件")
        print("4. 系统通知邮件")
        print("5. HTML邮件")
        print("6. 退出")
        
        choice = input("请输入选择 (1-6): ").strip()
        
        if choice == "6":
            print("演示结束")
            break
        
        # 获取目标邮箱
        target_email = input("请输入目标邮箱: ").strip()
        if not target_email:
            print("❌ 未输入目标邮箱")
            continue
        
        if choice == "1":
            # QQ风格邮件
            subject = input("请输入邮件标题: ").strip()
            content = input("请输入邮件内容: ").strip()
            qq_number = input("请输入QQ号码 (默认: 937359046): ").strip() or "937359046"
            
            success = sender.send_qq_style_email(target_email, subject, content, qq_number)
            
        elif choice == "2":
            # 自定义发件人邮件
            subject = input("请输入邮件标题: ").strip()
            content = input("请输入邮件内容: ").strip()
            display_name = input("请输入发件人名称: ").strip()
            display_email = input("请输入发件人邮箱: ").strip()
            
            success = sender.send_as_custom_sender(target_email, subject, content, display_name, display_email)
            
        elif choice == "3":
            # 商务邮件
            subject = input("请输入邮件标题: ").strip()
            content = input("请输入邮件内容: ").strip()
            role = input("请输入发件人角色 (默认: 客服): ").strip() or "客服"
            
            success = sender.send_business_email(target_email, subject, content, role)
            
        elif choice == "4":
            # 系统通知邮件
            title = input("请输入通知标题: ").strip()
            message = input("请输入通知内容: ").strip()
            
            success = sender.send_notification_email(target_email, title, message)
            
        elif choice == "5":
            # HTML邮件
            subject = input("请输入邮件标题: ").strip()
            html_content = input("请输入HTML内容: ").strip()
            display_name = input("请输入发件人名称: ").strip()
            display_email = input("请输入发件人邮箱: ").strip()
            
            success = sender.send_html_email_with_custom_sender(target_email, subject, html_content, display_name, display_email)
            
        else:
            print("无效选择")
            continue
        
        if success:
            print("✅ 邮件发送成功！")
        else:
            print("❌ 邮件发送失败！")


if __name__ == "__main__":
    main()
