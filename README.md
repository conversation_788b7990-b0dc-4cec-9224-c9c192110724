# 邮件发送工具

这是一个基于Python的邮件发送工具，支持通过163邮箱发送邮件到指定目标邮箱。

## 功能特点

- 支持发送纯文本和HTML格式邮件
- 图形用户界面，操作简单
- 支持抄送和密送功能
- 错误处理和状态提示
- 多线程发送，避免界面卡顿

## 文件说明

- `email_sender.py` - 核心邮件发送模块
- `email_gui.py` - 图形用户界面
- `demo.py` - 演示脚本
- `test_email.py` - 测试文件
- `build_exe.py` - Windows打包脚本
- `package_for_windows.py` - Windows打包准备脚本
- `requirements.txt` - 依赖包列表
- `README.md` - 使用说明

## 使用前准备

### 1. 开启163邮箱SMTP服务

1. 登录163邮箱网页版
2. 进入"设置" -> "POP3/SMTP/IMAP"
3. 开启"SMTP服务"
4. 设置授权码（不是登录密码）
5. 记住这个授权码，程序中需要使用

### 2. 安装Python

确保您的系统已安装Python 3.6或更高版本。

## 使用方法

### 方法一：使用图形界面（推荐）

```bash
python email_gui.py
```

1. 运行程序后会打开图形界面
2. 发送方邮箱默认为：<EMAIL>
3. 授权码已预配置
4. 输入目标邮箱地址
5. 输入邮件标题和内容
6. 点击"发送邮件"按钮

### 方法二：使用命令行

```bash
python email_sender.py
```

按照提示输入相关信息即可发送邮件。

### 方法三：在代码中使用

```python
from email_sender import EmailSender

# 创建邮件发送器（授权码已预配置）
sender = EmailSender()

# 发送邮件
success = sender.send_email(
    to_email="<EMAIL>",
    subject="测试邮件",
    content="这是一封测试邮件"
)

if success:
    print("邮件发送成功！")
else:
    print("邮件发送失败！")
```

## 注意事项

1. **授权码已预配置**：程序已内置163邮箱授权码
2. **网络连接**：确保网络连接正常，能够访问163邮箱服务器
3. **邮箱格式**：确保邮箱地址格式正确
4. **发送频率**：避免短时间内大量发送邮件，可能被邮箱服务商限制

## 常见问题

### Q: 提示"邮件发送失败"怎么办？
A:
1. 检查网络连接
2. 检查目标邮箱地址是否正确
3. 确认163邮箱服务是否正常

### Q: 可以使用其他邮箱吗？
A: 可以，但需要修改`email_sender.py`中的SMTP服务器配置：
- QQ邮箱：smtp.qq.com:587
- Gmail：smtp.gmail.com:587
- Outlook：smtp-mail.outlook.com:587

## 扩展功能

如需更多功能，可以考虑：
- 添加附件支持
- 邮件模板功能
- 批量发送功能
- 定时发送功能
- 邮件发送记录

## Windows可执行文件打包

使用提供的打包脚本可以将程序打包为Windows可执行文件：

```bash
python build_exe.py
```

或使用Windows打包准备脚本：

```bash
python package_for_windows.py
```

## 技术支持

如有问题，请检查：
1. Python版本是否为3.6+
2. 网络连接是否正常
3. 邮箱配置是否正确
4. 查看控制台错误信息
