#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的QQ邮箱发送测试
"""

from qq_email_sender import QQEmailSender
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def main():
    print("=== 简单QQ邮箱发送测试 ===")
    
    try:
        # 创建QQ邮箱发送器
        qq_sender = QQEmailSender(qq_auth_code='frkpmkdrehsxbdac')
        print("✅ QQ邮箱发送器创建成功")
        
        # 发送测试邮件
        print("📧 正在发送测试邮件...")
        success = qq_sender.send_as_qq(
            to_email='<EMAIL>',
            subject='QQ邮箱修复测试',
            content='这是修复后的QQ邮箱发送测试邮件。\n\n发送时间: 2025-07-24',
            sender_name='修复测试'
        )
        
        if success:
            print("✅ 邮件发送成功！")
        else:
            print("❌ 邮件发送失败！")
            
    except Exception as e:
        print(f"❌ 测试过程中出现异常: {e}")

if __name__ == "__main__":
    main()
