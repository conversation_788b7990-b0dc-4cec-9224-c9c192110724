#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
打包脚本 - 将邮件发送工具打包成Windows可执行文件
"""

import os
import sys
import subprocess
import shutil


def check_pyinstaller():
    """检查PyInstaller是否已安装"""
    try:
        import PyInstaller
        print("✅ PyInstaller已安装")
        return True
    except ImportError:
        print("❌ PyInstaller未安装")
        return False


def install_pyinstaller():
    """安装PyInstaller"""
    print("正在安装PyInstaller...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
        print("✅ PyInstaller安装成功")
        return True
    except subprocess.CalledProcessError:
        print("❌ PyInstaller安装失败")
        return False


def create_spec_file():
    """创建PyInstaller规格文件"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['email_gui.py'],
    pathex=[],
    binaries=[],
    datas=[],
    hiddenimports=[
        'tkinter',
        'tkinter.ttk',
        'tkinter.messagebox',
        'tkinter.scrolledtext',
        'email.mime.text',
        'email.mime.multipart',
        'email.header',
        'smtplib',
        'ssl',
        'threading',
        'logging'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='邮件发送工具',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # 不显示控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,  # 可以添加图标文件路径
)
'''
    
    with open('email_tool.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ 规格文件创建成功: email_tool.spec")


def build_exe():
    """构建可执行文件"""
    print("开始构建可执行文件...")
    
    try:
        # 使用规格文件构建
        subprocess.check_call([
            sys.executable, "-m", "PyInstaller",
            "--clean",
            "email_tool.spec"
        ])
        
        print("✅ 构建成功！")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 构建失败: {e}")
        return False


def create_portable_version():
    """创建便携版本"""
    print("创建便携版本...")
    
    # 创建便携版目录
    portable_dir = "邮件发送工具_便携版"
    if os.path.exists(portable_dir):
        shutil.rmtree(portable_dir)
    
    os.makedirs(portable_dir)
    
    # 复制可执行文件
    exe_path = os.path.join("dist", "邮件发送工具.exe")
    if os.path.exists(exe_path):
        shutil.copy2(exe_path, portable_dir)
        print(f"✅ 可执行文件已复制到: {portable_dir}")
    else:
        print("❌ 找不到可执行文件")
        return False
    
    # 创建使用说明
    readme_content = """# 邮件发送工具 - 便携版

## 使用说明

1. 双击 "邮件发送工具.exe" 启动程序
2. 程序已预配置163邮箱发送方信息：
   - 发送方邮箱: <EMAIL>
   - 授权码: 已预设
3. 在界面中输入目标邮箱、邮件标题和内容
4. 点击"发送邮件"按钮即可发送

## 注意事项

- 确保电脑连接互联网
- 目标邮箱地址格式要正确
- 如果发送失败，请检查网络连接

## 系统要求

- Windows 7 或更高版本
- 无需安装Python环境

## 技术支持

如有问题，请检查：
1. 网络连接是否正常
2. 目标邮箱地址是否正确
3. 防火墙是否阻止程序联网
"""
    
    with open(os.path.join(portable_dir, "使用说明.txt"), 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print(f"✅ 便携版创建完成: {portable_dir}")
    return True


def main():
    """主函数"""
    print("=== 邮件发送工具打包程序 ===")
    print("目标: 创建Windows可执行文件")
    
    # 检查当前系统
    if sys.platform != "win32":
        print("⚠️  当前不是Windows系统，建议在Windows环境下打包")
        print("   或者使用交叉编译工具")
    
    # 检查必要文件
    required_files = ['email_gui.py', 'email_sender.py']
    for file in required_files:
        if not os.path.exists(file):
            print(f"❌ 缺少必要文件: {file}")
            return
    
    print("✅ 必要文件检查通过")
    
    # 检查并安装PyInstaller
    if not check_pyinstaller():
        if not install_pyinstaller():
            print("无法安装PyInstaller，请手动安装:")
            print("pip install pyinstaller")
            return
    
    # 创建规格文件
    create_spec_file()
    
    # 构建可执行文件
    if build_exe():
        # 创建便携版
        if create_portable_version():
            print("\n🎉 打包完成！")
            print("生成的文件:")
            print("- dist/邮件发送工具.exe (单个可执行文件)")
            print("- 邮件发送工具_便携版/ (便携版文件夹)")
            print("\n可以将便携版文件夹复制到任何Windows电脑上使用")
        else:
            print("便携版创建失败，但可执行文件已生成在dist目录")
    else:
        print("打包失败，请检查错误信息")


if __name__ == "__main__":
    main()
