#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
QQ邮箱发送器测试脚本
演示如何使用您的QQ邮箱****************发送邮件
"""

from qq_email_sender import QQEmailSender, get_qq_auth_code_guide
import logging

# 设置详细日志
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def test_qq_sender_basic():
    """基础QQ邮箱发送测试"""
    print("=== 基础QQ邮箱发送测试 ===")
    
    # 注意：这里需要您提供真实的QQ邮箱授权码
    print("⚠️ 需要QQ邮箱授权码才能测试")
    print("如果您还没有授权码，请按照以下指南获取：")
    print(get_qq_auth_code_guide())
    
    auth_code = input("\n请输入QQ邮箱授权码 (测试用，可以按Enter跳过): ").strip()
    
    if not auth_code:
        print("⏭️ 跳过真实发送测试")
        return False
    
    try:
        # 创建QQ邮箱发送器
        qq_sender = QQEmailSender(qq_auth_code=auth_code)
        
        # 测试发送到目标邮箱
        target_email = input("请输入目标邮箱: ").strip()
        if not target_email:
            target_email = "<EMAIL>"  # 默认测试邮箱
        
        # 发送测试邮件
        success = qq_sender.send_as_qq(
            to_email=target_email,
            subject="QQ邮箱发送测试",
            content="这是使用QQ邮箱****************发送的测试邮件。",
            sender_name="测试用户"
        )
        
        if success:
            print("✅ QQ邮箱发送测试成功！")
            return True
        else:
            print("❌ QQ邮箱发送测试失败！")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中出现异常: {e}")
        return False


def test_notification_emails():
    """测试通知邮件功能"""
    print("\n=== 通知邮件功能测试 ===")
    
    auth_code = input("请输入QQ邮箱授权码 (可以按Enter跳过): ").strip()
    
    if not auth_code:
        print("⏭️ 跳过通知邮件测试")
        return
    
    try:
        qq_sender = QQEmailSender(qq_auth_code=auth_code)
        target_email = input("请输入目标邮箱: ").strip() or "<EMAIL>"
        
        # 测试普通通知邮件
        print("\n📧 发送普通通知邮件...")
        success1 = qq_sender.send_notification_email(
            to_email=target_email,
            title="系统维护通知",
            message="系统将于今晚22:00-24:00进行维护，期间可能无法正常访问。"
        )
        
        # 测试HTML通知邮件
        print("📧 发送HTML通知邮件...")
        success2 = qq_sender.send_html_notification(
            to_email=target_email,
            title="重要更新",
            message="新版本已发布，包含多项功能改进和安全更新。"
        )
        
        if success1 and success2:
            print("✅ 通知邮件测试全部成功！")
        else:
            print("❌ 部分通知邮件发送失败")
            
    except Exception as e:
        print(f"❌ 通知邮件测试异常: {e}")


def test_custom_sender_names():
    """测试自定义发件人名称"""
    print("\n=== 自定义发件人名称测试 ===")
    
    auth_code = input("请输入QQ邮箱授权码 (可以按Enter跳过): ").strip()
    
    if not auth_code:
        print("⏭️ 跳过自定义发件人名称测试")
        return
    
    try:
        qq_sender = QQEmailSender(qq_auth_code=auth_code)
        target_email = input("请输入目标邮箱: ").strip() or "<EMAIL>"
        
        # 测试不同的发件人名称
        sender_names = [
            "客服小助手",
            "系统管理员", 
            "产品团队",
            "技术支持"
        ]
        
        for i, sender_name in enumerate(sender_names, 1):
            print(f"📧 测试发件人名称: {sender_name}")
            
            success = qq_sender.send_as_qq(
                to_email=target_email,
                subject=f"测试邮件 #{i} - 来自{sender_name}",
                content=f"这是来自{sender_name}的测试邮件。\n\n发件人显示名称: {sender_name}\n实际邮箱: <EMAIL>",
                sender_name=sender_name
            )
            
            if success:
                print(f"✅ {sender_name} 发送成功")
            else:
                print(f"❌ {sender_name} 发送失败")
                
    except Exception as e:
        print(f"❌ 自定义发件人名称测试异常: {e}")


def demo_email_templates():
    """演示邮件模板"""
    print("\n=== 邮件模板演示 ===")
    
    # 业务邮件模板
    business_template = """
尊敬的用户，

感谢您使用我们的服务。

本次邮件内容：
- 账户状态：正常
- 最后登录：{last_login}
- 安全等级：高

如有任何问题，请联系客服。

祝好！
客服团队
    """.strip()
    
    # 营销邮件模板
    marketing_template = """
🎉 限时优惠活动开始啦！

亲爱的用户，

我们为您准备了专属优惠：
✨ 全场商品8折优惠
🎁 满100元送精美礼品
🚚 全国包邮

活动时间：即日起至月底
优惠码：SAVE20

立即购买：https://example.com

不要错过这个机会哦！
    """.strip()
    
    print("📧 业务邮件模板：")
    print(business_template.format(last_login="2025-07-24 10:30:00"))
    
    print("\n📧 营销邮件模板：")
    print(marketing_template)
    
    print("\n💡 使用方法：")
    print("qq_sender.send_as_qq(")
    print("    to_email='<EMAIL>',")
    print("    subject='业务通知',")
    print("    content=business_template.format(last_login='2025-07-24'),")
    print("    sender_name='客服团队'")
    print(")")


def main():
    """主函数"""
    print("🚀 QQ邮箱发送器测试程序")
    print("=" * 50)
    print("📧 使用您的QQ邮箱 <EMAIL> 发送邮件")
    print("⚠️ 需要先获取QQ邮箱授权码")
    print("=" * 50)
    
    while True:
        print("\n请选择测试选项:")
        print("1. 基础QQ邮箱发送测试")
        print("2. 通知邮件功能测试")
        print("3. 自定义发件人名称测试")
        print("4. 查看邮件模板演示")
        print("5. 查看QQ邮箱授权码获取指南")
        print("6. 退出")
        
        choice = input("请输入选择 (1-6): ").strip()
        
        if choice == "1":
            test_qq_sender_basic()
        elif choice == "2":
            test_notification_emails()
        elif choice == "3":
            test_custom_sender_names()
        elif choice == "4":
            demo_email_templates()
        elif choice == "5":
            print(get_qq_auth_code_guide())
        elif choice == "6":
            print("测试结束，再见！")
            break
        else:
            print("无效选择，请重新输入")


if __name__ == "__main__":
    main()
