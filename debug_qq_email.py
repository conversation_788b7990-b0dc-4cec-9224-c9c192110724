#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
QQ邮箱发送调试脚本
用于诊断和修复QQ邮箱发送问题
"""

import smtplib
import ssl
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.header import Header
from email.utils import formatdate
import uuid
import logging

# 设置详细日志
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def test_qq_smtp_connection(qq_email: str, qq_auth_code: str):
    """测试QQ邮箱SMTP连接"""
    print("=== QQ邮箱SMTP连接测试 ===")
    
    # QQ邮箱SMTP配置选项
    smtp_configs = [
        {"server": "smtp.qq.com", "port": 587, "use_ssl": False, "name": "TLS连接"},
        {"server": "smtp.qq.com", "port": 465, "use_ssl": True, "name": "SSL连接"},
        {"server": "smtp.qq.com", "port": 25, "use_ssl": False, "name": "普通连接"},
    ]
    
    for config in smtp_configs:
        print(f"\n🔍 测试 {config['name']}: {config['server']}:{config['port']}")
        
        try:
            # 创建SSL上下文
            context = ssl.create_default_context()
            context.check_hostname = False
            context.verify_mode = ssl.CERT_NONE
            
            if config['use_ssl']:
                # SSL连接
                print("  📡 尝试SSL连接...")
                server = smtplib.SMTP_SSL(config['server'], config['port'], context=context)
            else:
                # TLS连接
                print("  📡 尝试TLS连接...")
                server = smtplib.SMTP(config['server'], config['port'])
                server.ehlo()
                server.starttls(context=context)
                server.ehlo()
            
            print("  🔐 尝试登录...")
            server.login(qq_email, qq_auth_code)
            
            print(f"  ✅ {config['name']} 连接成功！")
            server.quit()
            return config
            
        except Exception as e:
            print(f"  ❌ {config['name']} 连接失败: {e}")
            continue
    
    print("\n❌ 所有连接方式都失败了")
    return None


def send_test_email_direct(qq_email: str, qq_auth_code: str, to_email: str, config: dict):
    """直接发送测试邮件"""
    print(f"\n=== 使用 {config['name']} 发送测试邮件 ===")
    
    try:
        # 创建邮件
        message = MIMEMultipart()
        message["From"] = f"QQ邮箱测试 <{qq_email}>"
        message["To"] = to_email
        message["Subject"] = Header("QQ邮箱发送测试 - 连接修复", 'utf-8')
        message["Message-ID"] = f"<{uuid.uuid4()}@qq.com>"
        message["Date"] = formatdate(localtime=True)
        
        # 邮件内容
        content = f"""
QQ邮箱发送测试成功！

测试信息：
- 发送方：{qq_email}
- 接收方：{to_email}
- 连接方式：{config['name']}
- SMTP服务器：{config['server']}:{config['port']}
- 发送时间：{formatdate(localtime=True)}

如果您收到这封邮件，说明QQ邮箱发送功能已修复。

测试完成！
        """.strip()
        
        message.attach(MIMEText(content, 'plain', 'utf-8'))
        
        # 创建SSL上下文
        context = ssl.create_default_context()
        context.check_hostname = False
        context.verify_mode = ssl.CERT_NONE
        
        # 发送邮件
        if config['use_ssl']:
            with smtplib.SMTP_SSL(config['server'], config['port'], context=context) as server:
                server.login(qq_email, qq_auth_code)
                text = message.as_string()
                server.sendmail(qq_email, [to_email], text)
        else:
            with smtplib.SMTP(config['server'], config['port']) as server:
                server.ehlo()
                server.starttls(context=context)
                server.ehlo()
                server.login(qq_email, qq_auth_code)
                text = message.as_string()
                server.sendmail(qq_email, [to_email], text)
        
        print("✅ 邮件发送成功！")
        return True
        
    except Exception as e:
        print(f"❌ 邮件发送失败: {e}")
        logger.error(f"发送失败详情: {e}")
        return False


def test_qq_email_sender_class():
    """测试修复后的QQ邮箱发送器类"""
    print("\n=== 测试QQ邮箱发送器类 ===")
    
    try:
        from qq_email_sender import QQEmailSender
        
        # 获取授权码
        auth_code = input("请输入QQ邮箱授权码: ").strip()
        if not auth_code:
            print("❌ 未输入授权码")
            return False
        
        # 创建发送器
        qq_sender = QQEmailSender(qq_auth_code=auth_code)
        
        # 获取目标邮箱
        target_email = input("请输入目标邮箱: ").strip()
        if not target_email:
            target_email = "<EMAIL>"
        
        # 发送测试邮件
        success = qq_sender.send_as_qq(
            to_email=target_email,
            subject="QQ邮箱发送器修复测试",
            content="这是使用修复后的QQ邮箱发送器发送的测试邮件。",
            sender_name="测试用户"
        )
        
        if success:
            print("✅ QQ邮箱发送器测试成功！")
            return True
        else:
            print("❌ QQ邮箱发送器测试失败！")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中出现异常: {e}")
        logger.error(f"测试异常: {e}")
        return False


def diagnose_qq_email_issues():
    """诊断QQ邮箱问题"""
    print("🔧 QQ邮箱发送问题诊断工具")
    print("=" * 40)
    
    # 检查常见问题
    print("\n📋 常见问题检查清单：")
    print("1. ✅ QQ邮箱是否已开启SMTP服务？")
    print("2. ✅ 授权码是否正确（16位字符）？")
    print("3. ✅ 网络连接是否正常？")
    print("4. ✅ 防火墙是否阻止SMTP连接？")
    
    # 获取用户信息
    qq_email = input("\n请输入QQ邮箱地址 (默认: <EMAIL>): ").strip()
    if not qq_email:
        qq_email = "<EMAIL>"
    
    qq_auth_code = input("请输入QQ邮箱授权码: ").strip()
    if not qq_auth_code:
        print("❌ 未输入授权码，无法进行测试")
        return
    
    target_email = input("请输入测试目标邮箱: ").strip()
    if not target_email:
        target_email = "<EMAIL>"
    
    # 测试SMTP连接
    working_config = test_qq_smtp_connection(qq_email, qq_auth_code)
    
    if working_config:
        print(f"\n🎉 找到可用的连接配置: {working_config['name']}")
        
        # 尝试发送测试邮件
        if send_test_email_direct(qq_email, qq_auth_code, target_email, working_config):
            print("\n✅ QQ邮箱发送功能正常！")
            
            # 测试发送器类
            test_qq_email_sender_class()
        else:
            print("\n❌ 邮件发送仍然失败")
    else:
        print("\n❌ 无法建立SMTP连接")
        print("\n🔧 建议检查：")
        print("1. 确认QQ邮箱已开启SMTP服务")
        print("2. 重新生成授权码")
        print("3. 检查网络连接")
        print("4. 尝试关闭防火墙")


def main():
    """主函数"""
    print("🚀 QQ邮箱发送问题修复工具")
    print("=" * 50)
    
    while True:
        print("\n请选择操作:")
        print("1. 诊断QQ邮箱发送问题")
        print("2. 测试SMTP连接")
        print("3. 测试QQ邮箱发送器类")
        print("4. 查看QQ邮箱配置指南")
        print("5. 退出")
        
        choice = input("请输入选择 (1-5): ").strip()
        
        if choice == "1":
            diagnose_qq_email_issues()
        elif choice == "2":
            qq_email = input("QQ邮箱地址: ").strip() or "<EMAIL>"
            auth_code = input("授权码: ").strip()
            if auth_code:
                test_qq_smtp_connection(qq_email, auth_code)
        elif choice == "3":
            test_qq_email_sender_class()
        elif choice == "4":
            print("""
📧 QQ邮箱SMTP配置指南：

1. 登录QQ邮箱 (https://mail.qq.com)
2. 设置 → 账户 → POP3/IMAP/SMTP服务
3. 开启"POP3/SMTP服务"
4. 发送短信验证
5. 获取16位授权码

SMTP服务器配置：
- 服务器：smtp.qq.com
- 端口：587 (TLS) 或 465 (SSL)
- 加密：TLS/SSL
            """)
        elif choice == "5":
            print("修复工具结束")
            break
        else:
            print("无效选择")


if __name__ == "__main__":
    main()
