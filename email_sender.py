#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
邮件发送功能模块
支持发送HTML和纯文本邮件
"""

import smtplib
import ssl
import uuid
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.header import <PERSON><PERSON>
from email.utils import formatdate
from typing import List, Optional
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class EmailSender:
    """增强版邮件发送类 - 支持多邮箱服务商"""

    # 邮箱服务商SMTP配置
    SMTP_CONFIGS = {
        "163.com": {"server": "smtp.163.com", "port": 465, "ssl": True},
        "qq.com": {"server": "smtp.qq.com", "port": 465, "ssl": True},  # QQ邮箱使用465端口+SSL
        "gmail.com": {"server": "smtp.gmail.com", "port": 587, "ssl": False},
        "outlook.com": {"server": "smtp-mail.outlook.com", "port": 587, "ssl": False},
        "hotmail.com": {"server": "smtp-mail.outlook.com", "port": 587, "ssl": False},
        "126.com": {"server": "smtp.126.com", "port": 465, "ssl": True},
        "sina.com": {"server": "smtp.sina.com", "port": 465, "ssl": True},
    }

    def __init__(self, sender_email: str = "<EMAIL>", sender_password: str = "FPszbLrZiv8Vm7Z3"):
        """
        初始化邮件发送器

        Args:
            sender_email: 发送方邮箱
            sender_password: 发送方邮箱密码或授权码
        """
        self.sender_email = sender_email
        self.sender_password = sender_password

        # 自动检测邮箱服务商并配置SMTP
        self._configure_smtp()

    def _configure_smtp(self):
        """根据邮箱地址自动配置SMTP服务器"""
        domain = self.sender_email.split('@')[1].lower()

        if domain in self.SMTP_CONFIGS:
            config = self.SMTP_CONFIGS[domain]
            self.smtp_server = config["server"]
            self.smtp_port = config["port"]
            self.use_ssl = config["ssl"]
        else:
            # 默认使用163邮箱配置
            self.smtp_server = "smtp.163.com"
            self.smtp_port = 465
            self.use_ssl = True
            logger.warning(f"未知邮箱域名 {domain}，使用默认163邮箱配置")
        
    def send_email(self,
                   to_email: str,
                   subject: str,
                   content: str,
                   content_type: str = "plain",
                   cc_emails: Optional[List[str]] = None,
                   bcc_emails: Optional[List[str]] = None,
                   sender_name: Optional[str] = None) -> bool:
        """
        发送邮件

        Args:
            to_email: 收件人邮箱
            subject: 邮件主题
            content: 邮件内容
            content_type: 内容类型 ("plain" 或 "html")
            cc_emails: 抄送邮箱列表
            bcc_emails: 密送邮箱列表
            sender_name: 发件人显示名称（可选）

        Returns:
            bool: 发送成功返回True，失败返回False
        """
        try:
            # 创建邮件对象
            message = MIMEMultipart()

            # 设置发件人信息，支持自定义显示名称
            if sender_name:
                from_header = f"{sender_name} <{self.sender_email}>"
            else:
                from_header = self.sender_email

            message["From"] = from_header
            message["To"] = to_email
            message["Subject"] = Header(subject, 'utf-8')
            # 添加额外的邮件头信息
            message["Message-ID"] = f"<{uuid.uuid4()}@{self.sender_email.split('@')[1]}>"
            message["Date"] = formatdate(localtime=True)
            # 添加发件人显示名称
            message["Sender"] = self.sender_email
            
            # 添加抄送
            if cc_emails:
                message["Cc"] = Header(", ".join(cc_emails), 'utf-8')
            
            # 添加邮件内容
            message.attach(MIMEText(content, content_type, 'utf-8'))
            
            # 准备收件人列表
            recipients = [to_email]
            if cc_emails:
                recipients.extend(cc_emails)
            if bcc_emails:
                recipients.extend(bcc_emails)
            
            # 创建SSL上下文，提高兼容性
            context = ssl.create_default_context()
            # 对于某些邮箱服务商，需要降低安全要求
            context.check_hostname = False
            context.verify_mode = ssl.CERT_NONE

            # 根据配置选择连接方式
            if self.use_ssl:
                # 使用SSL连接
                with smtplib.SMTP_SSL(self.smtp_server, self.smtp_port, context=context) as server:
                    server.login(self.sender_email, self.sender_password)
                    text = message.as_string()
                    server.sendmail(self.sender_email, recipients, text)
            else:
                # 使用TLS连接
                with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
                    server.ehlo()  # 发送EHLO命令
                    server.starttls(context=context)
                    server.ehlo()  # 再次发送EHLO命令
                    server.login(self.sender_email, self.sender_password)
                    text = message.as_string()
                    server.sendmail(self.sender_email, recipients, text)
                
            logger.info(f"邮件发送成功: {to_email}")
            return True
            
        except Exception as e:
            logger.error(f"邮件发送失败: {str(e)}")
            return False
    
    def send_html_email(self, to_email: str, subject: str, html_content: str) -> bool:
        """
        发送HTML格式邮件
        
        Args:
            to_email: 收件人邮箱
            subject: 邮件主题
            html_content: HTML格式的邮件内容
            
        Returns:
            bool: 发送成功返回True，失败返回False
        """
        return self.send_email(to_email, subject, html_content, "html")


def main():
    """主函数 - 示例用法"""
    # 注意：使用163邮箱需要开启SMTP服务并使用授权码，不是登录密码
    sender_password = input("请输入163邮箱的授权码: ")
    
    # 创建邮件发送器
    email_sender = EmailSender(sender_password=sender_password)
    
    # 获取用户输入
    to_email = input("请输入目标邮箱: ")
    subject = input("请输入邮件标题: ")
    content = input("请输入邮件内容: ")
    
    # 发送邮件
    success = email_sender.send_email(to_email, subject, content)
    
    if success:
        print("✅ 邮件发送成功！")
    else:
        print("❌ 邮件发送失败！")


if __name__ == "__main__":
    main()
