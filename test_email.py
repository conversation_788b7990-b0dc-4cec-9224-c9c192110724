#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
邮件发送功能测试文件
"""

import unittest
from unittest.mock import patch, MagicMock
from email_sender import EmailSender


class TestEmailSender(unittest.TestCase):
    """邮件发送器测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.sender = EmailSender(
            sender_email="<EMAIL>",
            sender_password="test_password"
        )
    
    def test_init(self):
        """测试初始化"""
        self.assertEqual(self.sender.sender_email, "<EMAIL>")
        self.assertEqual(self.sender.sender_password, "test_password")
        self.assertEqual(self.sender.smtp_server, "smtp.163.com")
        self.assertEqual(self.sender.smtp_port, 465)
    
    @patch('email_sender.smtplib.SMTP_SSL')
    def test_send_email_success(self, mock_smtp):
        """测试邮件发送成功"""
        # 模拟SMTP服务器
        mock_server = MagicMock()
        mock_smtp.return_value.__enter__.return_value = mock_server
        
        # 测试发送邮件
        result = self.sender.send_email(
            to_email="<EMAIL>",
            subject="测试邮件",
            content="这是测试内容"
        )
        
        # 验证结果
        self.assertTrue(result)
        mock_server.login.assert_called_once_with("<EMAIL>", "test_password")
        mock_server.sendmail.assert_called_once()
    
    @patch('email_sender.smtplib.SMTP_SSL')
    def test_send_email_failure(self, mock_smtp):
        """测试邮件发送失败"""
        # 模拟SMTP连接失败
        mock_smtp.side_effect = Exception("连接失败")
        
        # 测试发送邮件
        result = self.sender.send_email(
            to_email="<EMAIL>",
            subject="测试邮件",
            content="这是测试内容"
        )
        
        # 验证结果
        self.assertFalse(result)
    
    @patch('email_sender.smtplib.SMTP_SSL')
    def test_send_html_email(self, mock_smtp):
        """测试发送HTML邮件"""
        # 模拟SMTP服务器
        mock_server = MagicMock()
        mock_smtp.return_value.__enter__.return_value = mock_server
        
        # 测试发送HTML邮件
        html_content = "<h1>测试HTML邮件</h1><p>这是HTML内容</p>"
        result = self.sender.send_html_email(
            to_email="<EMAIL>",
            subject="HTML测试邮件",
            html_content=html_content
        )
        
        # 验证结果
        self.assertTrue(result)
        mock_server.login.assert_called_once()
        mock_server.sendmail.assert_called_once()


def run_manual_test():
    """手动测试函数"""
    print("=== 邮件发送功能手动测试 ===")
    print("注意：这将发送真实邮件，请确保配置正确")
    
    # 获取用户输入
    sender_password = input("请输入163邮箱授权码: ")
    if not sender_password:
        print("未输入授权码，跳过手动测试")
        return
    
    to_email = input("请输入测试目标邮箱: ")
    if not to_email:
        print("未输入目标邮箱，跳过手动测试")
        return
    
    # 创建邮件发送器
    sender = EmailSender(sender_password=sender_password)
    
    # 发送测试邮件
    print("正在发送测试邮件...")
    success = sender.send_email(
        to_email=to_email,
        subject="邮件发送功能测试",
        content="这是一封测试邮件，用于验证邮件发送功能是否正常工作。\n\n发送时间：" + 
                str(__import__('datetime').datetime.now())
    )
    
    if success:
        print("✅ 测试邮件发送成功！")
    else:
        print("❌ 测试邮件发送失败！")


if __name__ == "__main__":
    print("选择测试模式：")
    print("1. 单元测试（模拟测试）")
    print("2. 手动测试（发送真实邮件）")
    
    choice = input("请输入选择 (1/2): ").strip()
    
    if choice == "1":
        # 运行单元测试
        unittest.main(argv=[''], exit=False, verbosity=2)
    elif choice == "2":
        # 运行手动测试
        run_manual_test()
    else:
        print("无效选择，运行单元测试")
        unittest.main(argv=[''], exit=False, verbosity=2)
