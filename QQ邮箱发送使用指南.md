# QQ邮箱发送功能使用指南

## 📧 功能概述

本工具提供了**合法且安全**的方式使用您的QQ邮箱 `<EMAIL>` 发送邮件到目标邮箱。

### ✅ 合法性说明
- 使用您自己的QQ邮箱账户
- 通过官方SMTP服务发送
- 需要QQ邮箱授权码认证
- 符合邮件服务商规范

## 🚀 快速开始

### 1. 获取QQ邮箱授权码

**步骤：**
1. 登录 [QQ邮箱](https://mail.qq.com)
2. 点击右上角"设置" → "账户"
3. 找到"POP3/IMAP/SMTP/Exchange/CardDAV/CalDAV服务"
4. 开启"POP3/SMTP服务"或"IMAP/SMTP服务"
5. 按照提示发送短信验证
6. 获得16位授权码（如：`abcdefghijklmnop`）

**⚠️ 重要提醒：**
- 授权码不是QQ密码
- 请妥善保管授权码
- 每次重新生成会使旧授权码失效

### 2. 基础使用

```python
from qq_email_sender import QQEmailSender

# 创建QQ邮箱发送器
qq_sender = QQEmailSender(
    qq_email="<EMAIL>",
    qq_auth_code="您的16位授权码"
)

# 发送邮件
success = qq_sender.send_as_qq(
    to_email="<EMAIL>",
    subject="邮件标题",
    content="邮件内容",
    sender_name="自定义发件人名称"
)
```

## 📋 功能特性

### 1. 自定义发件人显示名称

```python
# <AUTHOR> <EMAIL>
qq_sender.send_as_qq(
    to_email="<EMAIL>",
    subject="客服回复",
    content="您的问题已处理完成",
    sender_name="客服小助手"
)
```

### 2. 系统通知邮件

```python
# 发送系统通知
qq_sender.send_notification_email(
    to_email="<EMAIL>",
    title="系统维护通知",
    message="系统将于今晚进行维护"
)
```

### 3. HTML格式邮件

```python
# 发送HTML通知邮件
qq_sender.send_html_notification(
    to_email="<EMAIL>",
    title="重要更新",
    message="新版本已发布，包含多项改进"
)
```

## 🛠️ 高级功能

### 1. 多邮箱服务商支持

系统自动识别并配置不同邮箱服务商：

```python
from email_sender import EmailSender

# 自动配置QQ邮箱
qq_sender = EmailSender("<EMAIL>", "qq_auth_code")

# 自动配置163邮箱
netease_sender = EmailSender("<EMAIL>", "netease_auth_code")

# 自动配置Gmail
gmail_sender = EmailSender("<EMAIL>", "gmail_password")
```

### 2. 配置管理

```python
from email_config import EmailConfig

# 安全保存邮箱配置
config = EmailConfig()
config.add_email_account(
    name="qq_main",
    email="<EMAIL>",
    password="您的授权码"
)

# 获取配置
account = config.get_account("qq_main")
```

## 📝 使用示例

### 示例1：客服邮件

```python
from qq_email_sender import QQEmailSender

qq_sender = QQEmailSender(qq_auth_code="您的授权码")

# 客服回复邮件
qq_sender.send_as_qq(
    to_email="<EMAIL>",
    subject="关于您的咨询 - 订单问题",
    content="""
尊敬的客户，

感谢您的咨询。关于您提到的订单问题，我们已经为您处理：

1. 订单状态已更新
2. 物流信息已同步
3. 预计明天送达

如有其他问题，请随时联系我们。

祝好！
客服团队
    """,
    sender_name="客服小王"
)
```

### 示例2：营销邮件

```python
# HTML营销邮件
html_content = """
<div style="font-family: Arial; max-width: 600px;">
    <h2 style="color: #1aad19;">🎉 限时优惠活动</h2>
    <p>亲爱的用户，</p>
    <div style="background: #f0f8ff; padding: 15px; border-radius: 5px;">
        <h3>专属优惠：</h3>
        <ul>
            <li>✨ 全场商品8折</li>
            <li>🎁 满100送礼品</li>
            <li>🚚 全国包邮</li>
        </ul>
    </div>
    <p><strong>优惠码：SAVE20</strong></p>
</div>
"""

qq_sender.send_as_qq(
    to_email="<EMAIL>",
    subject="🎉 专属优惠来啦！",
    content=html_content,
    content_type="html",
    sender_name="营销团队"
)
```

### 示例3：批量发送

```python
# 批量发送通知
recipients = [
    "<EMAIL>",
    "<EMAIL>", 
    "<EMAIL>"
]

for email in recipients:
    success = qq_sender.send_notification_email(
        to_email=email,
        title="系统升级通知",
        message="系统将于本周末进行升级维护"
    )
    
    if success:
        print(f"✅ 发送到 {email} 成功")
    else:
        print(f"❌ 发送到 {email} 失败")
```

## 🧪 测试工具

### 1. 运行测试脚本

```bash
# 基础测试
python test_qq_sender.py

# QQ邮箱专用测试
python test_qq_email.py

# 配置管理
python email_config.py
```

### 2. 测试功能

- ✅ 基础邮件发送
- ✅ HTML邮件发送
- ✅ 自定义发件人名称
- ✅ 系统通知邮件
- ✅ 批量发送测试

## ⚠️ 注意事项

### 1. 安全建议

- **不要**在代码中硬编码授权码
- 使用配置文件或环境变量存储敏感信息
- 定期更换授权码
- 不要分享授权码给他人

### 2. 发送限制

- QQ邮箱有发送频率限制
- 建议控制发送速度（每分钟不超过30封）
- 避免发送垃圾邮件内容
- 遵守邮件服务商规范

### 3. 错误处理

```python
try:
    success = qq_sender.send_as_qq(...)
    if not success:
        print("发送失败，请检查网络和配置")
except Exception as e:
    print(f"发送异常: {e}")
```

## 🔧 故障排除

### 常见问题

1. **授权码错误**
   - 检查授权码是否正确
   - 确认已开启SMTP服务
   - 重新生成授权码

2. **连接失败**
   - 检查网络连接
   - 确认防火墙设置
   - 验证SMTP服务器地址

3. **邮件被拒收**
   - 检查邮件内容是否合规
   - 避免垃圾邮件关键词
   - 确认收件人邮箱有效

## 📞 技术支持

如果遇到问题，请：

1. 查看日志输出
2. 检查配置信息
3. 运行测试脚本
4. 参考错误信息进行排查

## 🎯 最佳实践

1. **邮件内容**
   - 使用清晰的主题行
   - 内容简洁明了
   - 避免全大写文字

2. **发送策略**
   - 控制发送频率
   - 分批发送大量邮件
   - 监控发送成功率

3. **用户体验**
   - 提供退订链接
   - 个性化邮件内容
   - 及时回复用户咨询

---

**🎉 现在您可以安全、合法地使用QQ邮箱发送邮件了！**
